using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomPropertyDrawer(typeof(SentenceDisplayData))]
public class SentenceDisplayDataDrawer : PropertyDrawer
{
    private static Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
    private static Dictionary<string, int> _tabStates = new Dictionary<string, int>();

    private readonly string[] _tabNames = { "Text", "Visual", "Typewriter", "Effects", "Events", "Control" };
    private readonly Color _headerColor = new Color(0.7f, 0.9f, 1f, 0.3f);
    private readonly Color _warningColor = new Color(1f, 0.8f, 0.4f, 0.3f);
    private readonly Color _tabBackgroundColor = new Color(0.9f, 0.9f, 0.9f, 0.1f);

    // Spacing constants
    private const float HEADER_HEIGHT = 22f;
    private const float TAB_HEIGHT = 25f;
    private const float LINE_SPACING = 3f;
    private const float SECTION_SPACING = 8f;
    private const float TEXT_AREA_HEIGHT = 40f;

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        string key = GetPropertyKey(property);
        bool isExpanded = _foldoutStates.ContainsKey(key) ? _foldoutStates[key] : false;

        if (!isExpanded)
            return HEADER_HEIGHT + 2f;

        // Calculate height dynamically based on current tab content
        int currentTab = _tabStates.ContainsKey(key) ? _tabStates[key] : 0;
        float height = HEADER_HEIGHT + TAB_HEIGHT + SECTION_SPACING;

        height += CalculateTabContentHeight(property, currentTab);

        return height + SECTION_SPACING; // Extra bottom padding
    }

    private float CalculateTabContentHeight(SerializedProperty property, int tabIndex)
    {
        float height = 0f;

        switch (tabIndex)
        {
            case 0: // Text
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Text label
                height += TEXT_AREA_HEIGHT + SECTION_SPACING; // Text area
                height += GetPropertyHeight(property, "SentenceID"); // Sentence ID
                break;

            case 1: // Visual
                // Color section
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "TextColor");
                height += GetPropertyHeight(property, "FollowBaseColor");
                height += LINE_SPACING;

                // Font section
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "FontAsset");
                height += GetPropertyHeight(property, "FontSize");
                height += GetPropertyHeight(property, "FollowBaseFontSize");
                height += LINE_SPACING;

                // Style section
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "IsBold");
                height += GetPropertyHeight(property, "IsItalic");
                height += GetPropertyHeight(property, "Alignment");
                break;

            case 2: // Typewriter
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "UseTypewriter");

                SerializedProperty useTypewriter = property.FindPropertyRelative("UseTypewriter");
                if (useTypewriter != null && useTypewriter.boolValue)
                {
                    height += LINE_SPACING;
                    height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                    height += GetPropertyHeight(property, "DelayPerCharacter");
                    height += GetPropertyHeight(property, "PauseOnPunctuation");
                    height += GetPropertyHeight(property, "PunctuationDelay");
                    height += GetPropertyHeight(property, "TypewriterSound");
                }

                height += SECTION_SPACING;
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "DelayAfterSentence");
                break;

            case 3: // Effects
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Display Effects header
                height += GetPropertyHeight(property, "TextEffects");
                height += GetPropertyHeight(property, "TextMaterial");
                height += SECTION_SPACING;
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Delete Effects header
                height += GetPropertyHeight(property, "DeleteEffects");
                height += SECTION_SPACING;
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Image header
                height += GetPropertyHeight(property, "ImageSprite");
                break;

            case 4: // Events
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "OnSentenceStart");
                height += GetPropertyHeight(property, "OnSentenceEnd");
                break;

            case 5: // Control
                // Control Settings section
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "RequireSkipToAdvance");
                height += SECTION_SPACING;

                // Action Type section
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "Action");
                height += GetPropertyHeight(property, "TargetSentenceID");
                height += GetPropertyHeight(property, "DeleteSpeed");
                height += GetPropertyHeight(property, "DeleteSound");
                height += SECTION_SPACING;

                // Replace System section
                height += EditorGUIUtility.singleLineHeight + LINE_SPACING; // Section header
                height += GetPropertyHeight(property, "ReplacementOptions");
                height += GetPropertyHeight(property, "CurrentReplacementIndex");
                height += GetPropertyHeight(property, "LoopReplacements");
                height += GetPropertyHeight(property, "ReplaceDelay");
                break;
        }

        return height + SECTION_SPACING; // Extra bottom padding
    }

    private float GetPropertyHeight(SerializedProperty parentProperty, string propertyName)
    {
        SerializedProperty prop = parentProperty.FindPropertyRelative(propertyName);
        if (prop != null)
        {
            return EditorGUI.GetPropertyHeight(prop, true) + LINE_SPACING;
        }
        return 0f;
    }
    
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);

        string key = GetPropertyKey(property);
        bool isExpanded = _foldoutStates.ContainsKey(key) ? _foldoutStates[key] : false;

        // Get text property for preview
        SerializedProperty textProp = property.FindPropertyRelative("Text");
        string previewText = textProp?.stringValue ?? "";
        if (string.IsNullOrEmpty(previewText))
            previewText = "[Empty Sentence]";
        else if (previewText.Length > 40)
            previewText = previewText[..40] + "...";

        // Header with colored background and proper height
        Rect headerRect = new Rect(position.x, position.y, position.width, HEADER_HEIGHT);
        EditorGUI.DrawRect(headerRect, _headerColor);

        // Foldout with sentence preview - add padding for better appearance
        Rect foldoutRect = new Rect(headerRect.x + 4, headerRect.y + 2, headerRect.width - 8, headerRect.height - 4);
        string foldoutLabel = $"📝 {previewText}";
        isExpanded = EditorGUI.Foldout(foldoutRect, isExpanded, foldoutLabel, true, EditorStyles.foldoutHeader);
        _foldoutStates[key] = isExpanded;

        if (!isExpanded)
        {
            EditorGUI.EndProperty();
            return;
        }

        // Content area with proper spacing
        float contentY = position.y + HEADER_HEIGHT + 2f;
        Rect contentRect = new Rect(position.x, contentY, position.width, position.height - HEADER_HEIGHT - 2f);

        EditorGUI.indentLevel++;
        DrawExpandedContent(contentRect, property, key);
        EditorGUI.indentLevel--;

        EditorGUI.EndProperty();
    }
    
    private void DrawExpandedContent(Rect contentRect, SerializedProperty property, string key)
    {
        float yOffset = 0;

        // Tab selection with background
        int currentTab = _tabStates.ContainsKey(key) ? _tabStates[key] : 0;
        Rect tabRect = new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, TAB_HEIGHT);

        // Draw tab background
        EditorGUI.DrawRect(tabRect, _tabBackgroundColor);

        // Draw tabs with padding
        Rect tabButtonRect = new Rect(tabRect.x + 2, tabRect.y + 2, tabRect.width - 4, tabRect.height - 4);
        currentTab = GUI.Toolbar(tabButtonRect, currentTab, _tabNames);
        _tabStates[key] = currentTab;
        yOffset += TAB_HEIGHT + LINE_SPACING;

        // Tab content area with padding
        Rect tabContentRect = new Rect(contentRect.x + 4, contentRect.y + yOffset,
                                      contentRect.width - 8, contentRect.height - yOffset);

        // Draw content based on selected tab
        switch (currentTab)
        {
            case 0: DrawTextTab(tabContentRect, property); break;
            case 1: DrawVisualTab(tabContentRect, property); break;
            case 2: DrawTypewriterTab(tabContentRect, property); break;
            case 3: DrawEffectsTab(tabContentRect, property); break;
            case 4: DrawEventsTab(tabContentRect, property); break;
            case 5: DrawControlTab(tabContentRect, property); break;
        }
    }
    
    private void DrawTextTab(Rect rect, SerializedProperty property)
    {
        float yOffset = 0;

        // Text field with better height
        SerializedProperty textProp = property.FindPropertyRelative("Text");
        if (textProp != null)
        {
            Rect textRect = new Rect(rect.x, rect.y + yOffset, rect.width, TEXT_AREA_HEIGHT);
            EditorGUI.LabelField(new Rect(textRect.x, textRect.y, textRect.width, EditorGUIUtility.singleLineHeight),
                               "📝 Text Content", EditorStyles.boldLabel);

            Rect textAreaRect = new Rect(textRect.x, textRect.y + EditorGUIUtility.singleLineHeight + 2,
                                       textRect.width, TEXT_AREA_HEIGHT - EditorGUIUtility.singleLineHeight - 2);
            textProp.stringValue = EditorGUI.TextArea(textAreaRect, textProp.stringValue);
            yOffset += TEXT_AREA_HEIGHT + SECTION_SPACING;
        }

        // Sentence ID with proper spacing
        DrawPropertyWithSpacing(rect, property, "SentenceID", "🆔 Sentence ID", ref yOffset);
    }

    private void DrawVisualTab(Rect rect, SerializedProperty property)
    {
        float yOffset = 0;

        // Color section
        DrawSectionHeader(rect, "🎨 Color Settings", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "TextColor", "Text Color", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "FollowBaseColor", "Follow Base Color", ref yOffset);

        yOffset += LINE_SPACING;

        // Font section
        DrawSectionHeader(rect, "🔤 Font Settings", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "FontAsset", "Font Asset", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "FontSize", "Font Size", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "FollowBaseFontSize", "Follow Base Size", ref yOffset);

        yOffset += LINE_SPACING;

        // Style section
        DrawSectionHeader(rect, "🎭 Style Settings", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "IsBold", "Bold", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "IsItalic", "Italic", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "Alignment", "Alignment", ref yOffset);
    }
    
    private void DrawTypewriterTab(Rect rect, SerializedProperty property)
    {
        float yOffset = 0;

        DrawSectionHeader(rect, "⌨️ Typewriter Settings", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "UseTypewriter", "Use Typewriter", ref yOffset);

        SerializedProperty useTypewriter = property.FindPropertyRelative("UseTypewriter");
        if (useTypewriter != null && useTypewriter.boolValue)
        {
            yOffset += LINE_SPACING;
            DrawSectionHeader(rect, "⏱️ Timing Settings", ref yOffset);
            DrawPropertyWithSpacing(rect, property, "DelayPerCharacter", "Delay Per Character", ref yOffset);
            DrawPropertyWithSpacing(rect, property, "PauseOnPunctuation", "Pause On Punctuation", ref yOffset);
            DrawPropertyWithSpacing(rect, property, "PunctuationDelay", "Punctuation Delay", ref yOffset);
            DrawPropertyWithSpacing(rect, property, "TypewriterSound", "Typewriter Sound", ref yOffset);
        }

        yOffset += SECTION_SPACING;
        DrawSectionHeader(rect, "⏳ After Sentence", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "DelayAfterSentence", "Delay After Sentence", ref yOffset);
    }

    private void DrawEffectsTab(Rect rect, SerializedProperty property)
    {
        float yOffset = 0;

        DrawSectionHeader(rect, "✨ Display Effects", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "TextEffects", "Text Effects (Normal)", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "TextMaterial", "Text Material", ref yOffset);

        yOffset += SECTION_SPACING;
        DrawSectionHeader(rect, "🗑️ Delete Effects", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "DeleteEffects", "Delete Effects", ref yOffset);

        yOffset += SECTION_SPACING;
        DrawSectionHeader(rect, "🖼️ Image", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "ImageSprite", "Image Sprite", ref yOffset);
    }

    private void DrawEventsTab(Rect rect, SerializedProperty property)
    {
        float yOffset = 0;

        DrawSectionHeader(rect, "🎬 Unity Events", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "OnSentenceStart", "On Sentence Start", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "OnSentenceEnd", "On Sentence End", ref yOffset);
    }

    private void DrawControlTab(Rect rect, SerializedProperty property)
    {
        float yOffset = 0;

        // Control section
        DrawSectionHeader(rect, "⚙️ Control Settings", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "RequireSkipToAdvance", "Require Skip To Advance", ref yOffset);

        yOffset += SECTION_SPACING;

        // Action section
        DrawSectionHeader(rect, "🎬 Action Type", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "Action", "Action", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "TargetSentenceID", "Target Sentence ID", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "DeleteSpeed", "Delete Speed", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "DeleteSound", "Delete Sound", ref yOffset);

        yOffset += SECTION_SPACING;

        // Replace system section
        DrawSectionHeader(rect, "🔄 Replace System", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "ReplacementOptions", "Replacement Options", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "CurrentReplacementIndex", "Current Index (-1 = Auto)", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "LoopReplacements", "Loop Replacements", ref yOffset);
        DrawPropertyWithSpacing(rect, property, "ReplaceDelay", "Replace Delay", ref yOffset);
    }
    
    // Helper methods for better spacing and organization
    private void DrawSectionHeader(Rect rect, string title, ref float yOffset, Color? backgroundColor = null)
    {
        Rect headerRect = new Rect(rect.x, rect.y + yOffset, rect.width, EditorGUIUtility.singleLineHeight);

        // Draw background if provided
        if (backgroundColor.HasValue)
        {
            EditorGUI.DrawRect(headerRect, backgroundColor.Value);
        }

        EditorGUI.LabelField(headerRect, title, EditorStyles.boldLabel);
        yOffset += EditorGUIUtility.singleLineHeight + LINE_SPACING;
    }

    private void DrawPropertyWithSpacing(Rect rect, SerializedProperty property, string propertyName, string label, ref float yOffset)
    {
        SerializedProperty prop = property.FindPropertyRelative(propertyName);
        if (prop != null)
        {
            float propertyHeight = EditorGUI.GetPropertyHeight(prop, true);
            Rect propRect = new Rect(rect.x, rect.y + yOffset, rect.width, propertyHeight);

            EditorGUI.PropertyField(propRect, prop, new GUIContent(label), true);
            yOffset += propertyHeight + LINE_SPACING;
        }
    }



    private string GetPropertyKey(SerializedProperty property)
    {
        return $"{property.serializedObject.targetObject.GetInstanceID()}_{property.propertyPath}";
    }
}
