%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PixelRect:
    serializedVersion: 2
    x: 0
    y: 43
    width: 1920
    height: 997
  m_ShowMode: 4
  m_Title: Hierarchy
  m_RootView: {fileID: 2}
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_Maximized: 1
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12008, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  - {fileID: 5}
  - {fileID: 4}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 997
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_UseTopView: 1
  m_TopViewHeight: 36
  m_UseBottomView: 1
  m_BottomViewHeight: 20
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12011, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 36
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
  m_LastLoadedLayoutName: 
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12042, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 977
    width: 1920
    height: 20
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 1920
    height: 941
  m_MinSize: {x: 300, y: 100}
  m_MaxSize: {x: 24288, y: 16192}
  vertical: 1
  controlID: 139
  draggingID: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 7}
  - {fileID: 8}
  - {fileID: 11}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1920
    height: 941
  m_MinSize: {x: 300, y: 100}
  m_MaxSize: {x: 24288, y: 16192}
  vertical: 0
  controlID: 140
  draggingID: 0
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 401
    height: 941
  m_MinSize: {x: 201, y: 226}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 14}
  m_Panes:
  - {fileID: 14}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 9}
  - {fileID: 10}
  m_Position:
    serializedVersion: 2
    x: 401
    y: 0
    width: 922
    height: 941
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 8096, y: 16192}
  vertical: 1
  controlID: 141
  draggingID: 0
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 922
    height: 463
  m_MinSize: {x: 102, y: 126}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 16}
  m_Panes:
  - {fileID: 15}
  - {fileID: 16}
  - {fileID: 17}
  m_Selected: 1
  m_LastSelected: 0
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 463
    width: 922
    height: 478
  m_MinSize: {x: 52, y: 76}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 20}
  m_Panes:
  - {fileID: 18}
  - {fileID: 19}
  - {fileID: 20}
  - {fileID: 21}
  - {fileID: 22}
  - {fileID: 23}
  - {fileID: 13}
  - {fileID: 12}
  m_Selected: 2
  m_LastSelected: 0
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1323
    y: 0
    width: 597
    height: 941
  m_MinSize: {x: 276, y: 126}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 24}
  m_Panes:
  - {fileID: 24}
  - {fileID: 25}
  - {fileID: 26}
  m_Selected: 0
  m_LastSelected: 2
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13202, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 1000, y: 500}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Frame Debugger
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Frame Debugger\u200B"
  m_Pos:
    serializedVersion: 2
    x: 401
    y: 542
    width: 920
    height: 452
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_TreeWidth: 337.92
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: -1089619856830078684, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Profiler\u200B"
  m_Pos:
    serializedVersion: 2
    x: 402
    y: 542
    width: 919
    height: 452
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Recording: 1
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 5625797050680475732
  - rid: 5625797050680475733
  - rid: 5625797050680475734
  - rid: 5625797050680475735
  - rid: 5625797050680475736
  - rid: 5625797050680475737
  - rid: 5625797050680475738
  - rid: 5625797050680475739
  - rid: 5625797050680475740
  - rid: 5625797050680475741
  - rid: 5625797050680475742
  - rid: 5625797050680475743
  - rid: 5625797050680475746
  - rid: 5625797050680475744
  - rid: 5625797050680475745
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 0
  references:
    version: 2
    RefIds:
    - rid: 5625797050680475732
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 1
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 02000000
            m_LastClickedID: 2
            m_ExpandedIDs: 04000000
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns:
            - width: 230
              sortedAscending: 1
              headerContent:
                m_Text: Overview
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Overview\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 200
              maxWidth: 1000000
              autoResize: 1
              allowToggleVisibility: 0
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Total
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Total\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Calls
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Calls\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: GC Alloc
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "GC Alloc\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Time ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Time ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 25
              sortedAscending: 0
              headerContent:
                m_Text: 
                m_Image: {fileID: -5161429177145976760, guid: 0000000000000000d000000000000000, type: 0}
                m_Tooltip: Warnings
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 25
              maxWidth: 25
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            m_VisibleColumns: 0000000001000000020000000300000004000000050000000600000007000000
            m_SortedColumns: 05000000
          m_ThreadIndexInThreadNames: 1
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: -1
            realSizes:
            - 0
            - 0
            relativeSizes:
            - 0.7
            - 0.3
            minSizes:
            - 450
            - 50
            maxSizes:
            - 0
            - 0
            lastTotalSize: 0
            splitSize: 6
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: -1
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 7264
          <threadIndex>k__BackingField: 0
          m_GroupName: 
    - rid: 5625797050680475733
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 5625797050680475734
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475735
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 0
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 0
          - 0
          relativeSizes:
          - 0.7
          - 0.3
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 0
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 5625797050680475736
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 5625797050680475737
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475738
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475739
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475740
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475741
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475742
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475743
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 5625797050680475747
    - rid: 5625797050680475744
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475745
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 5625797050680475746
      type: {class: AddressablesProfilerModule, ns: UnityEditor.AddressableAssets.Diagnostics, asm: Unity.Addressables.Editor}
      data:
        m_Identifier: UnityEditor.AddressableAssets.Diagnostics.AddressablesProfilerModule,
          Unity.Addressables.Editor, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - rid: 5625797050680475747
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 24
    width: 400
    height: 915
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: e4faffff48e7000096e70000
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 7}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: 6bdc4fba4af82b74fb46af4b7f6a4e86
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 401
    y: 79
    width: 920
    height: 437
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames:
  - UnityEditor.DeviceSimulation.SimulatorWindow
  m_SerializedViewValues:
  - E:\Unity projetos\Huggable Horrors\Library\PlayModeViewStates\cc16378698719ad4f9213dae6f4aaa6d
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1920, y: 1080}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 0
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 03000000000000000000000000000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -960
    m_HBaseRangeMax: 960
    m_VBaseRangeMin: -540
    m_VBaseRangeMax: 540
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 0
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 920
      height: 416
    m_Scale: {x: 1, y: 1}
    m_Translation: {x: 530.83826, y: 289.1266}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -530.83826
      y: -289.1266
      width: 920
      height: 416
    m_MinimalGUI: 1
  m_defaultScale: 0.38518518
  m_LastWindowPixelSize: {x: 920, y: 437}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000000000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1
    y: 24
    width: 920
    height: 437
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: Tool Settings
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-141.0,"y":-154.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -141, y: -154}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 2
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-transform-toolbar
      index: 0
      contents: '{"m_Layout":2,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":99.3333740234375,"y":-216.33334350585938},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":2,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 99.333374, y: -216.33334}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 2
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-component-tools
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 1
      id: Orientation
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":97.0,"y":33.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: 97, y: 33}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Particles
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap
      index: 11
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: AINavigationOverlay
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: Scene View/Animation Rigging
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-213.0,"y":41.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -213, y: 41}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Cinemachine Tool Settings
      index: 10
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay
      index: 16
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 265, y: 241}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 240, y: 135}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay (0)
      index: 16
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 240, y: 135}
      sizeOverridden: 1
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-234.0,"y":-212.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -234, y: -212}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Sprite Swap
      index: 9
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 236, y: 152}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Path
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Timeline Control
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Model
      index: 14
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Event Tester
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 1
      id: Overlays/OverlayMenu
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-317.0,"y":-113.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -317, y: -113}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: SceneView/CamerasOverlay
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-256.0009765625,"y":-185.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":252.0,"y":180.0},"m_SizeOverridden":true}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -256.00098, y: -185}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 252, y: 180}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: 5ea3426d808c7224f96521bb55bbeb81
  m_Gizmos: 1
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: 960, y: 540, z: 0}
    speed: 2
    m_Value: {x: 960, y: 540, z: 0}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 1
    showFog: 1
    showSkybox: 1
    showFlares: 1
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 0
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    yGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    m_ShowGrid: 0
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: 0.025163632, y: -0.007333778, z: -0.0048664026, w: -1.0002927}
    speed: 2
    m_Value: {x: 0.02516363, y: -0.0073337774, z: -0.004866402, w: -1.0002927}
  m_Size:
    m_Target: 1101.4536
    speed: 2
    m_Value: 1101.4536
  m_Ortho:
    m_Target: 0
    speed: 2
    m_Value: 0
  m_CameraSettings:
    m_Speed: 2
    m_SpeedNormalized: 1
    m_SpeedMin: 0.01
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 0
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: -0.025878506, y: -0.03014034, z: -0.001529442, w: -0.99967736}
  m_LastSceneViewOrtho: 0
  m_Viewpoint:
    m_SceneView: {fileID: 16}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 0
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: 
    section: 
  m_ViewIsLockedToObject: 0
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 924ffcbe75518854f97b48776d0f1939, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: 3D UI GameObject (nothing loaded)
    m_Image: {fileID: 2800000, guid: 7129268cf102b2f45809905bcb27ce8b, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "3D UI GameObject (nothing loaded)\u200B"
  m_Pos:
    serializedVersion: 2
    x: 402
    y: 79
    width: 919
    height: 437
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Selected: f81f27d80c4951f4e9af03ffe5765cdf
  m_GraphObject: {fileID: 0}
  m_LastSerializedFileContents: "{\n    \"m_SGVersion\": 3,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GraphData\",\n   
    \"m_ObjectId\": \"4b2d39095b8741d58cef3d38d55e1f1b\",\n    \"m_Properties\":
    [\n        {\n            \"m_Id\": \"292d1919291d410489337c647c1a64b2\"\n       
    },\n        {\n            \"m_Id\": \"d95897c126284153826db2fbfc1c7729\"\n       
    },\n        {\n            \"m_Id\": \"f65887f5163e49a8bddd9e7f7cd8053f\"\n       
    }\n    ],\n    \"m_Keywords\": [],\n    \"m_Dropdowns\": [],\n    \"m_CategoryData\":
    [\n        {\n            \"m_Id\": \"ceab22b886714561888df00cfd3aef15\"\n       
    }\n    ],\n    \"m_Nodes\": [\n        {\n            \"m_Id\": \"9887c87afc664ae293b30fa68ea36493\"\n       
    },\n        {\n            \"m_Id\": \"6df15905c8784220bf33ca7ba28eccee\"\n       
    },\n        {\n            \"m_Id\": \"11b73ff0fa154a4ab7b7643b3177582c\"\n       
    },\n        {\n            \"m_Id\": \"f9cc89d2657247ea865e2acf5b0260f2\"\n       
    },\n        {\n            \"m_Id\": \"8ed51f854f494728858593a3444d8b75\"\n       
    },\n        {\n            \"m_Id\": \"64bfbdb92b144195b79f975303620831\"\n       
    },\n        {\n            \"m_Id\": \"25975bedd0c74f9f94ae10f7b660c531\"\n       
    },\n        {\n            \"m_Id\": \"c836079eecc44167bdbacb66aa7786a2\"\n       
    },\n        {\n            \"m_Id\": \"f38bdccc227148f6b5954817abef7f47\"\n       
    },\n        {\n            \"m_Id\": \"3b5c73d3fe9641a0a1b53801969aae33\"\n       
    },\n        {\n            \"m_Id\": \"f3e6469215b5470e8b15a0efc36f535e\"\n       
    },\n        {\n            \"m_Id\": \"392167fa0ca741559f2a3e4aedcb3f96\"\n       
    },\n        {\n            \"m_Id\": \"3c81f7a54d8b40e182692462e1a82732\"\n       
    },\n        {\n            \"m_Id\": \"7ab740bd50324f5d80dc357fbe8b8c08\"\n       
    },\n        {\n            \"m_Id\": \"e7dc3bc1251a4e4fb358d327cc4269f7\"\n       
    },\n        {\n            \"m_Id\": \"8b5a94fbbc17497d8e62299acba83c53\"\n       
    },\n        {\n            \"m_Id\": \"0f68894bbd44419e9b3c07efd99724ce\"\n       
    },\n        {\n            \"m_Id\": \"e3b40896758447e5ad924a00aed0caec\"\n       
    },\n        {\n            \"m_Id\": \"2e3285e174b2439c9c6d42b1ba66a935\"\n       
    },\n        {\n            \"m_Id\": \"f167fb43700741478a9164302433a0b7\"\n       
    },\n        {\n            \"m_Id\": \"c72e7019c51c4b8aac9400aafccaa084\"\n       
    },\n        {\n            \"m_Id\": \"13cc9fda1fb745dd90040b6b24c2973a\"\n       
    },\n        {\n            \"m_Id\": \"2bd95a9384bc40529477704f566017ea\"\n       
    },\n        {\n            \"m_Id\": \"8e627f1c95154619b6329e2f2d9cd550\"\n       
    },\n        {\n            \"m_Id\": \"524787901e574eff947ead5ce0bcdd0e\"\n       
    },\n        {\n            \"m_Id\": \"a18af33f83fc4e1196b98e8309c2d5ac\"\n       
    },\n        {\n            \"m_Id\": \"9f6a8c7b776b4e9c967ec349f7cb7aec\"\n       
    },\n        {\n            \"m_Id\": \"5d99edb7c2464bf9b0d35d3bd7767d7d\"\n       
    },\n        {\n            \"m_Id\": \"c59ea2727b394e3491eb5d6f13bef194\"\n       
    },\n        {\n            \"m_Id\": \"f7570d19ef694e49aa8155a6a7f5b128\"\n       
    },\n        {\n            \"m_Id\": \"9c0eb40a474043bb9db15f3253e2a9eb\"\n       
    },\n        {\n            \"m_Id\": \"19260fbd29cd4371969a5235205e31a8\"\n       
    },\n        {\n            \"m_Id\": \"62472c701c6a4e7db9d89192798b6fac\"\n       
    },\n        {\n            \"m_Id\": \"9679dd298ef7416d9642e2bf4513f2ec\"\n       
    },\n        {\n            \"m_Id\": \"833509ffe4874d8fb033b1b89fedf1e4\"\n       
    },\n        {\n            \"m_Id\": \"3929fda5bcc64677b65de48d7825ff80\"\n       
    },\n        {\n            \"m_Id\": \"5556653824a14cb8be1629a9b8916b16\"\n       
    }\n    ],\n    \"m_GroupDatas\": [\n        {\n            \"m_Id\": \"ae815c96fb9547a493f35d348aa0d75a\"\n       
    },\n        {\n            \"m_Id\": \"f788b5c3888e46cf93e5fdd47f47ff37\"\n       
    },\n        {\n            \"m_Id\": \"a54a7856b4454dd58a722529d29c370d\"\n       
    },\n        {\n            \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n       
    }\n    ],\n    \"m_StickyNoteDatas\": [],\n    \"m_Edges\": [\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"0f68894bbd44419e9b3c07efd99724ce\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"f3e6469215b5470e8b15a0efc36f535e\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"13cc9fda1fb745dd90040b6b24c2973a\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"2bd95a9384bc40529477704f566017ea\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"13cc9fda1fb745dd90040b6b24c2973a\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"f3e6469215b5470e8b15a0efc36f535e\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"13cc9fda1fb745dd90040b6b24c2973a\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"5d99edb7c2464bf9b0d35d3bd7767d7d\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"13cc9fda1fb745dd90040b6b24c2973a\"\n                },\n                \"m_SlotId\":
    4\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9679dd298ef7416d9642e2bf4513f2ec\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"19260fbd29cd4371969a5235205e31a8\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"62472c701c6a4e7db9d89192798b6fac\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"2bd95a9384bc40529477704f566017ea\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"392167fa0ca741559f2a3e4aedcb3f96\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"2e3285e174b2439c9c6d42b1ba66a935\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"c72e7019c51c4b8aac9400aafccaa084\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"392167fa0ca741559f2a3e4aedcb3f96\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"833509ffe4874d8fb033b1b89fedf1e4\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3929fda5bcc64677b65de48d7825ff80\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"5556653824a14cb8be1629a9b8916b16\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"3c81f7a54d8b40e182692462e1a82732\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"f3e6469215b5470e8b15a0efc36f535e\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"524787901e574eff947ead5ce0bcdd0e\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9f6a8c7b776b4e9c967ec349f7cb7aec\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"5556653824a14cb8be1629a9b8916b16\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3b5c73d3fe9641a0a1b53801969aae33\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"5d99edb7c2464bf9b0d35d3bd7767d7d\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3929fda5bcc64677b65de48d7825ff80\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"62472c701c6a4e7db9d89192798b6fac\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"c59ea2727b394e3491eb5d6f13bef194\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"7ab740bd50324f5d80dc357fbe8b8c08\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"392167fa0ca741559f2a3e4aedcb3f96\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"833509ffe4874d8fb033b1b89fedf1e4\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"5556653824a14cb8be1629a9b8916b16\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"8b5a94fbbc17497d8e62299acba83c53\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"0f68894bbd44419e9b3c07efd99724ce\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"8e627f1c95154619b6329e2f2d9cd550\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"5d99edb7c2464bf9b0d35d3bd7767d7d\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"9679dd298ef7416d9642e2bf4513f2ec\"\n                },\n                \"m_SlotId\":
    1\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"c59ea2727b394e3491eb5d6f13bef194\"\n               
    },\n                \"m_SlotId\": 2\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"9c0eb40a474043bb9db15f3253e2a9eb\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"62472c701c6a4e7db9d89192798b6fac\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"9f6a8c7b776b4e9c967ec349f7cb7aec\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"5d99edb7c2464bf9b0d35d3bd7767d7d\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"a18af33f83fc4e1196b98e8309c2d5ac\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"9f6a8c7b776b4e9c967ec349f7cb7aec\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"c59ea2727b394e3491eb5d6f13bef194\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"3929fda5bcc64677b65de48d7825ff80\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"c72e7019c51c4b8aac9400aafccaa084\"\n                },\n                \"m_SlotId\":
    2\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"392167fa0ca741559f2a3e4aedcb3f96\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"e3b40896758447e5ad924a00aed0caec\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"13cc9fda1fb745dd90040b6b24c2973a\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"e7dc3bc1251a4e4fb358d327cc4269f7\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"0f68894bbd44419e9b3c07efd99724ce\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"f167fb43700741478a9164302433a0b7\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"c72e7019c51c4b8aac9400aafccaa084\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"f3e6469215b5470e8b15a0efc36f535e\"\n                },\n                \"m_SlotId\":
    3\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"833509ffe4874d8fb033b1b89fedf1e4\"\n               
    },\n                \"m_SlotId\": 1\n            }\n        },\n        {\n           
    \"m_OutputSlot\": {\n                \"m_Node\": {\n                    \"m_Id\":
    \"f7570d19ef694e49aa8155a6a7f5b128\"\n                },\n                \"m_SlotId\":
    0\n            },\n            \"m_InputSlot\": {\n                \"m_Node\":
    {\n                    \"m_Id\": \"c59ea2727b394e3491eb5d6f13bef194\"\n               
    },\n                \"m_SlotId\": 0\n            }\n        }\n    ],\n    \"m_VertexContext\":
    {\n        \"m_Position\": {\n            \"x\": -333.9998779296875,\n           
    \"y\": 739.9999389648438\n        },\n        \"m_Blocks\": [\n            {\n               
    \"m_Id\": \"9887c87afc664ae293b30fa68ea36493\"\n            },\n            {\n               
    \"m_Id\": \"6df15905c8784220bf33ca7ba28eccee\"\n            },\n            {\n               
    \"m_Id\": \"11b73ff0fa154a4ab7b7643b3177582c\"\n            }\n        ]\n   
    },\n    \"m_FragmentContext\": {\n        \"m_Position\": {\n            \"x\":
    -333.9998779296875,\n            \"y\": 940.0\n        },\n        \"m_Blocks\":
    [\n            {\n                \"m_Id\": \"f9cc89d2657247ea865e2acf5b0260f2\"\n           
    },\n            {\n                \"m_Id\": \"8ed51f854f494728858593a3444d8b75\"\n           
    },\n            {\n                \"m_Id\": \"64bfbdb92b144195b79f975303620831\"\n           
    },\n            {\n                \"m_Id\": \"25975bedd0c74f9f94ae10f7b660c531\"\n           
    },\n            {\n                \"m_Id\": \"c836079eecc44167bdbacb66aa7786a2\"\n           
    },\n            {\n                \"m_Id\": \"f38bdccc227148f6b5954817abef7f47\"\n           
    },\n            {\n                \"m_Id\": \"3b5c73d3fe9641a0a1b53801969aae33\"\n           
    }\n        ]\n    },\n    \"m_PreviewData\": {\n        \"serializedMesh\": {\n           
    \"m_SerializedMesh\": \"{\\\"mesh\\\":{\\\"instanceID\\\":0}}\",\n           
    \"m_Guid\": \"\"\n        },\n        \"preventRotation\": false\n    },\n   
    \"m_Path\": \"Shader Graphs\",\n    \"m_GraphPrecision\": 1,\n    \"m_PreviewMode\":
    2,\n    \"m_OutputNode\": {\n        \"m_Id\": \"\"\n    },\n    \"m_SubDatas\":
    [],\n    \"m_ActiveTargets\": [\n        {\n            \"m_Id\": \"ce4f2724893d447490729e3f5ddfa7d1\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"048f3c6d57644c979d5ebf80472d4edf\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"_FadeDistance\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"05427a293164462ca391a1ce809750e1\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"0a41789aa9d947df9806a26ef72405cb\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Edge2\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Edge2\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\": 1.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.NormalMaterialSlot\",\n    \"m_ObjectId\": \"0d8274c6b4794566b33f20a5b1777f0b\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Normal\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Normal\",\n    \"m_StageCapability\":
    1,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\":
    \"0f68894bbd44419e9b3c07efd99724ce\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f788b5c3888e46cf93e5fdd47f47ff37\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1471.0001220703125,\n            \"y\": 725.0000610351563,\n           
    \"width\": 208.0,\n            \"height\": 302.00006103515627\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"b863d40447f144bb8fdb95f3fb026d42\"\n       
    },\n        {\n            \"m_Id\": \"fef73bb032e2437f96461dfb1d846cd9\"\n       
    },\n        {\n            \"m_Id\": \"fcd3b85a2f7f4d17851b30bdf38d3d34\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n       
    \"plus\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"0f8c97037b3a44a19beeb82d92ea1428\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\":
    \"11a65f902cba4a65815e8c7c1bad684f\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"e00\": 0.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 0.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 0.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"e00\": 1.0,\n       
    \"e01\": 0.0,\n        \"e02\": 0.0,\n        \"e03\": 0.0,\n        \"e10\":
    0.0,\n        \"e11\": 1.0,\n        \"e12\": 0.0,\n        \"e13\": 0.0,\n       
    \"e20\": 0.0,\n        \"e21\": 0.0,\n        \"e22\": 1.0,\n        \"e23\":
    0.0,\n        \"e30\": 0.0,\n        \"e31\": 0.0,\n        \"e32\": 0.0,\n       
    \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"11b73ff0fa154a4ab7b7643b3177582c\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.Tangent\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"c98eca849ce844fea8e3a1d7448d044e\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Tangent\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"128c0995529b4fd3be1dca16f9535da4\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\":
    0.0,\n        \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n       
    \"e12\": 0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\":
    0.0,\n        \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n       
    \"e31\": 0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SplitNode\",\n    \"m_ObjectId\":
    \"13cc9fda1fb745dd90040b6b24c2973a\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Split\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": -1955.0001220703125,\n            \"y\": 719.0,\n            \"width\":
    120.0,\n            \"height\": 149.00006103515626\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"5905493421bf44eb97b0d6b5e4855667\"\n       
    },\n        {\n            \"m_Id\": \"6805b4da98ad47ffb15e8ea08b116a15\"\n       
    },\n        {\n            \"m_Id\": \"c0a7c63af759452e9b17f1ea30380acd\"\n       
    },\n        {\n            \"m_Id\": \"3c0b6bef8c9342d7b0a62fff1140c402\"\n       
    },\n        {\n            \"m_Id\": \"1be523dc849d4bd28b4400f091c9cf8c\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"separate\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"13ea0678a2934ce5a65102f21303e424\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"19260fbd29cd4371969a5235205e31a8\",\n   
    \"m_Group\": {\n        \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1387.9998779296875,\n            \"y\": 1616.9998779296875,\n            \"width\":
    152.0,\n            \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"99055cdf6f3245dcbd6933c83954c422\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"f65887f5163e49a8bddd9e7f7cd8053f\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"1be523dc849d4bd28b4400f091c9cf8c\",\n    \"m_Id\": 4,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"1cd2932b444042f3a53f8bacd3de5849\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Edge1\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Edge1\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"25975bedd0c74f9f94ae10f7b660c531\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Smoothness\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"3b96655ff4af47cca1acd4c61fc021a3\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Smoothness\"\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty\",\n    \"m_ObjectId\":
    \"292d1919291d410489337c647c1a64b2\",\n    \"m_Guid\": {\n        \"m_GuidSerialized\":
    \"b61f0511-b0d1-4003-b81d-3ff9b67ebfe6\"\n    },\n    \"m_Name\": \"_MaskMin\",\n   
    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\": \"_MaskMin\",\n   
    \"m_DefaultReferenceName\": \"_MaskMin\",\n    \"m_OverrideReferenceName\": \"\",\n   
    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\": false,\n   
    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n    \"m_Precision\":
    0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"2932964ec90147089b0779d5d0431f2e\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_FadeDistance\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.OneMinusNode\",\n    \"m_ObjectId\": \"2bd95a9384bc40529477704f566017ea\",\n   
    \"m_Group\": {\n        \"m_Id\": \"ae815c96fb9547a493f35d348aa0d75a\"\n    },\n   
    \"m_Name\": \"One Minus\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1617.0001220703125,\n            \"y\": 314.0,\n            \"width\": 208.0,\n           
    \"height\": 278.00006103515627\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"b4c0481e0d5840d191f9e4ff8333a8af\"\n        },\n       
    {\n            \"m_Id\": \"3dc1bde3190f4664aebfd5c228538c86\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"complement\",\n        \"invert\",\n       
    \"opposite\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    2,\n    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalLitSubTarget\",\n   
    \"m_ObjectId\": \"2c40dcb158de49b096f908860c2a67c1\",\n    \"m_WorkflowMode\":
    1,\n    \"m_NormalDropOffSpace\": 0,\n    \"m_ClearCoat\": false,\n    \"m_BlendModePreserveSpecular\":
    true\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PositionMaterialSlot\",\n   
    \"m_ObjectId\": \"2ca81f7d9f8f488ab8e4921b26ce2bbd\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Position\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Position\",\n    \"m_StageCapability\": 1,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"2cb511b74825426880695c6fd3cd2daf\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"2d66b2fad7844b67af58a5852fe59644\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_MaskMin\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"2e3285e174b2439c9c6d42b1ba66a935\",\n    \"m_Group\": {\n       
    \"m_Id\": \"ae815c96fb9547a493f35d348aa0d75a\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1380.0001220703125,\n           
    \"y\": 392.0000305175781,\n            \"width\": 132.0,\n            \"height\":
    33.999969482421878\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"cd781165e39f4cd4bd5d19671f1622c5\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"d95897c126284153826db2fbfc1c7729\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"31afe94076bc4dd6ba3c55b920a908e5\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.NormalMaterialSlot\",\n    \"m_ObjectId\":
    \"36df693af5c2486aac03b9af397bd5e2\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Normal (Tangent Space)\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"NormalTS\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 3\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SmoothstepNode\",\n    \"m_ObjectId\":
    \"392167fa0ca741559f2a3e4aedcb3f96\",\n    \"m_Group\": {\n        \"m_Id\":
    \"ae815c96fb9547a493f35d348aa0d75a\"\n    },\n    \"m_Name\": \"Smoothstep\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -993.0000610351563,\n           
    \"y\": 306.0000305175781,\n            \"width\": 207.99993896484376,\n           
    \"height\": 326.0000305175781\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"890caff605ff49d9b8288c3e762bcb3d\"\n        },\n       
    {\n            \"m_Id\": \"b439e87b710e484d989a5d07a614a529\"\n        },\n       
    {\n            \"m_Id\": \"edf4e042ae9c440ebe9cd843b867e180\"\n        },\n       
    {\n            \"m_Id\": \"5b25624d8c7748c090439b61ac7823fb\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"curve\"\n    ],\n    \"m_Precision\": 0,\n   
    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n   
    \"m_ObjectId\": \"3929fda5bcc64677b65de48d7825ff80\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -979.9998779296875,\n            \"y\": 1132.0,\n           
    \"width\": 208.0,\n            \"height\": 301.9998779296875\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"11a65f902cba4a65815e8c7c1bad684f\"\n       
    },\n        {\n            \"m_Id\": \"aa8ad347dd884701bd2b096c80d6d593\"\n       
    },\n        {\n            \"m_Id\": \"717501f45b95483a9f1011a850df0fc5\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"3a8085611abf47c3965ec2e5e261ecaf\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_MaskMin\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"3b5c73d3fe9641a0a1b53801969aae33\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Alpha\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"d5095dea1be74c5e9775c8c9b765844c\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Alpha\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"3b96655ff4af47cca1acd4c61fc021a3\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Smoothness\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Smoothness\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": 0.5,\n    \"m_DefaultValue\": 0.5,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n   
    \"m_ObjectId\": \"3bab6fb353ec4b6fae056449f457a118\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Base Color\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"BaseColor\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": {\n        \"x\": 0.5,\n        \"y\": 0.5,\n        \"z\":
    0.5\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.5,\n        \"y\": 0.5,\n       
    \"z\": 0.5\n    },\n    \"m_Labels\": [],\n    \"m_ColorMode\": 0,\n    \"m_DefaultColor\":
    {\n        \"r\": 0.5,\n        \"g\": 0.5,\n        \"b\": 0.5,\n        \"a\":
    1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"3c0b6bef8c9342d7b0a62fff1140c402\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"3c81f7a54d8b40e182692462e1a82732\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f788b5c3888e46cf93e5fdd47f47ff37\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1615.9998779296875,\n           
    \"y\": 732.0,\n            \"width\": 129.0,\n            \"height\": 34.0\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"3a8085611abf47c3965ec2e5e261ecaf\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"292d1919291d410489337c647c1a64b2\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"3cfdfeeb9b35432d9e1c4c0c8d8fd0a4\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"3dc1bde3190f4664aebfd5c228538c86\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n   
    \"m_ObjectId\": \"3dc3b5c05e96425dbe5cc3d5e6143aac\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"_MaskMax\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"3ea2fa7382384519b37efcde54668249\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\":
    2.0,\n        \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n       
    \"e12\": 2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\":
    2.0,\n        \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n       
    \"e31\": 2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"48ced890a367436a8d2d2813ac161dc7\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_MaskMax\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n   
    \"m_ObjectId\": \"5164c27ddea2426cb5a9e68fdfd9e19c\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"_MaskMax\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"524787901e574eff947ead5ce0bcdd0e\",\n   
    \"m_Group\": {\n        \"m_Id\": \"a54a7856b4454dd58a722529d29c370d\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1612.0001220703125,\n            \"y\": 1181.0001220703125,\n            \"width\":
    129.0,\n            \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"2d66b2fad7844b67af58a5852fe59644\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"292d1919291d410489337c647c1a64b2\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"5556653824a14cb8be1629a9b8916b16\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -729.0,\n            \"y\": 983.9999389648438,\n           
    \"width\": 208.0,\n            \"height\": 301.99993896484377\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"a92293a0103e4c7e83384015352cc314\"\n       
    },\n        {\n            \"m_Id\": \"579cbc0477044076965547ab6d408c9f\"\n       
    },\n        {\n            \"m_Id\": \"128c0995529b4fd3be1dca16f9535da4\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"579cbc0477044076965547ab6d408c9f\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"5905493421bf44eb97b0d6b5e4855667\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"5b25624d8c7748c090439b61ac7823fb\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SmoothstepNode\",\n   
    \"m_ObjectId\": \"5d99edb7c2464bf9b0d35d3bd7767d7d\",\n    \"m_Group\": {\n       
    \"m_Id\": \"a54a7856b4454dd58a722529d29c370d\"\n    },\n    \"m_Name\": \"Smoothstep\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1239.0001220703125,\n           
    \"y\": 1134.0001220703125,\n            \"width\": 208.0,\n            \"height\":
    326.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"e4360ae57c884e1a8ab3eb6f1133555a\"\n        },\n        {\n            \"m_Id\":
    \"bcf32b4d775f48699b493eeba17c1f10\"\n        },\n        {\n            \"m_Id\":
    \"d7c7c0fe6f724b41a179a9cf22c12e9b\"\n        },\n        {\n            \"m_Id\":
    \"6fc76fc0a3fd402d8c4c63c7caa2e3df\"\n        }\n    ],\n    \"synonyms\": [\n       
    \"curve\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"5fd7ffd5266249e6aa7eaf75b330b97e\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"6012d804a2f944d88931ea45be8ec851\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 1.0,\n       
    \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\": 1.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SubtractNode\",\n   
    \"m_ObjectId\": \"62472c701c6a4e7db9d89192798b6fac\",\n    \"m_Group\": {\n       
    \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n    },\n    \"m_Name\": \"Subtract\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1220.9998779296875,\n           
    \"y\": 1572.9998779296875,\n            \"width\": 207.99993896484376,\n           
    \"height\": 301.9998779296875\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"13ea0678a2934ce5a65102f21303e424\"\n        },\n       
    {\n            \"m_Id\": \"775ae098754145e4962f2496cb49158a\"\n        },\n       
    {\n            \"m_Id\": \"8d98fed1496f4b13a1bee40d01e607d1\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"subtraction\",\n        \"remove\",\n       
    \"minus\",\n        \"take away\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"64bfbdb92b144195b79f975303620831\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Metallic\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"e5016cc40ebc4ed5b55713463d0cadb1\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Metallic\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"6805b4da98ad47ffb15e8ea08b116a15\",\n   
    \"m_Id\": 1,\n    \"m_DisplayName\": \"R\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"R\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"6df15905c8784220bf33ca7ba28eccee\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"VertexDescription.Normal\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"0d8274c6b4794566b33f20a5b1777f0b\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Normal\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"6fc76fc0a3fd402d8c4c63c7caa2e3df\",\n   
    \"m_Id\": 3,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"717501f45b95483a9f1011a850df0fc5\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"775ae098754145e4962f2496cb49158a\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"7ab740bd50324f5d80dc357fbe8b8c08\",\n   
    \"m_Group\": {\n        \"m_Id\": \"ae815c96fb9547a493f35d348aa0d75a\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1381.0001220703125,\n            \"y\": 323.0,\n            \"width\": 132.0,\n           
    \"height\": 34.00006103515625\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"5164c27ddea2426cb5a9e68fdfd9e19c\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"d95897c126284153826db2fbfc1c7729\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.MultiplyNode\",\n    \"m_ObjectId\":
    \"833509ffe4874d8fb033b1b89fedf1e4\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"Multiply\",\n    \"m_DrawState\": {\n       
    \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -981.9999389648438,\n            \"y\": 738.9999389648438,\n           
    \"width\": 207.99993896484376,\n            \"height\": 302.00006103515627\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"ffcea7418658489e896c403c7e6fdbb4\"\n       
    },\n        {\n            \"m_Id\": \"3ea2fa7382384519b37efcde54668249\"\n       
    },\n        {\n            \"m_Id\": \"8f00cc238ddc41d1b1d422d10a50e67d\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"multiplication\",\n        \"times\",\n       
    \"x\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"843094b108254daead36025349a58806\",\n    \"m_Id\": 3,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"8433cef959c14cb79cc70ca4119cf811\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n   
    \"m_ObjectId\": \"849defb8a28349cebad3bc4e7b474dc2\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"890caff605ff49d9b8288c3e762bcb3d\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Edge1\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Edge1\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\":
    \"8b5a94fbbc17497d8e62299acba83c53\",\n    \"m_Group\": {\n        \"m_Id\":
    \"f788b5c3888e46cf93e5fdd47f47ff37\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1611.9998779296875,\n           
    \"y\": 802.9999389648438,\n            \"width\": 152.0,\n            \"height\":
    34.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"2932964ec90147089b0779d5d0431f2e\"\n        }\n    ],\n    \"synonyms\": [],\n   
    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"f65887f5163e49a8bddd9e7f7cd8053f\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"8d98fed1496f4b13a1bee40d01e607d1\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"8e627f1c95154619b6329e2f2d9cd550\",\n   
    \"m_Group\": {\n        \"m_Id\": \"a54a7856b4454dd58a722529d29c370d\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1616.0001220703125,\n            \"y\": 1147.0001220703125,\n            \"width\":
    129.0,\n            \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"b589983fd25f4117b8985f3cd1974c16\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"292d1919291d410489337c647c1a64b2\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"8e75cd85e92c4f2face7d300bd1a0eec\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Ambient Occlusion\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Occlusion\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    1.0,\n    \"m_DefaultValue\": 1.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"8ed51f854f494728858593a3444d8b75\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.NormalTS\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"36df693af5c2486aac03b9af397bd5e2\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.NormalTS\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n    \"m_ObjectId\": \"8f00cc238ddc41d1b1d422d10a50e67d\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\":
    0.0,\n        \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n       
    \"e12\": 0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\":
    0.0,\n        \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n       
    \"e31\": 0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"90b37e6da5ab446e9604dd4b7b5b7067\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Edge2\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Edge2\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.OneMinusNode\",\n    \"m_ObjectId\": \"9679dd298ef7416d9642e2bf4513f2ec\",\n   
    \"m_Group\": {\n        \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n    },\n   
    \"m_Name\": \"One Minus\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1618.999755859375,\n            \"y\": 1574.9998779296875,\n            \"width\":
    207.9998779296875,\n            \"height\": 278.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"ac4136e3a9c94ee49b2aa2b80065eb3f\"\n       
    },\n        {\n            \"m_Id\": \"8433cef959c14cb79cc70ca4119cf811\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"complement\",\n        \"invert\",\n       
    \"opposite\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"9887c87afc664ae293b30fa68ea36493\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"VertexDescription.Position\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"2ca81f7d9f8f488ab8e4921b26ce2bbd\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"VertexDescription.Position\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"99055cdf6f3245dcbd6933c83954c422\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"_FadeDistance\",\n    \"m_SlotType\":
    1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\":
    3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"9c0eb40a474043bb9db15f3253e2a9eb\",\n    \"m_Group\": {\n       
    \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1381.9998779296875,\n           
    \"y\": 1652.9998779296875,\n            \"width\": 132.0001220703125,\n           
    \"height\": 33.9998779296875\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"3dc3b5c05e96425dbe5cc3d5e6143aac\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"d95897c126284153826db2fbfc1c7729\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.AddNode\",\n    \"m_ObjectId\":
    \"9f6a8c7b776b4e9c967ec349f7cb7aec\",\n    \"m_Group\": {\n        \"m_Id\":
    \"a54a7856b4454dd58a722529d29c370d\"\n    },\n    \"m_Name\": \"Add\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": -1456.0001220703125,\n            \"y\": 1144.0001220703125,\n           
    \"width\": 208.0,\n            \"height\": 302.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"b4968450afb74a4ea1142841229cfefc\"\n       
    },\n        {\n            \"m_Id\": \"3cfdfeeb9b35432d9e1c4c0c8d8fd0a4\"\n       
    },\n        {\n            \"m_Id\": \"5fd7ffd5266249e6aa7eaf75b330b97e\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"addition\",\n        \"sum\",\n       
    \"plus\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\":
    \"a098710806644fada95a65a003a9f1e8\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_FadeDistance\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\":
    0.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.PropertyNode\",\n    \"m_ObjectId\": \"a18af33f83fc4e1196b98e8309c2d5ac\",\n   
    \"m_Group\": {\n        \"m_Id\": \"a54a7856b4454dd58a722529d29c370d\"\n    },\n   
    \"m_Name\": \"Property\",\n    \"m_DrawState\": {\n        \"m_Expanded\": true,\n       
    \"m_Position\": {\n            \"serializedVersion\": \"2\",\n            \"x\":
    -1613.0001220703125,\n            \"y\": 1214.0001220703125,\n            \"width\":
    152.0,\n            \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"048f3c6d57644c979d5ebf80472d4edf\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"f65887f5163e49a8bddd9e7f7cd8053f\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\":
    \"a54a7856b4454dd58a722529d29c370d\",\n    \"m_Title\": \"Borda Esquerda\",\n   
    \"m_Position\": {\n        \"x\": -1641.0,\n        \"y\": 1075.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"a92293a0103e4c7e83384015352cc314\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"aa8ad347dd884701bd2b096c80d6d593\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 2.0,\n        \"e01\": 2.0,\n        \"e02\": 2.0,\n       
    \"e03\": 2.0,\n        \"e10\": 2.0,\n        \"e11\": 2.0,\n        \"e12\":
    2.0,\n        \"e13\": 2.0,\n        \"e20\": 2.0,\n        \"e21\": 2.0,\n       
    \"e22\": 2.0,\n        \"e23\": 2.0,\n        \"e30\": 2.0,\n        \"e31\":
    2.0,\n        \"e32\": 2.0,\n        \"e33\": 2.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"ac4136e3a9c94ee49b2aa2b80065eb3f\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n    \"m_ObjectId\": \"ada33b48087b42959b5a8b40cb8e543d\",\n   
    \"m_Title\": \"Borda Direita\",\n    \"m_Position\": {\n        \"x\": -1643.9998779296875,\n       
    \"y\": 1505.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n   
    \"m_ObjectId\": \"ae815c96fb9547a493f35d348aa0d75a\",\n    \"m_Title\": \"Borda
    Superior\",\n    \"m_Position\": {\n        \"x\": -1642.0,\n        \"y\": 247.0\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"b439e87b710e484d989a5d07a614a529\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"Edge2\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Edge2\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"b4968450afb74a4ea1142841229cfefc\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"b4c0481e0d5840d191f9e4ff8333a8af\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\":
    1.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"b589983fd25f4117b8985f3cd1974c16\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_MaskMin\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"b863d40447f144bb8fdb95f3fb026d42\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"bcf32b4d775f48699b493eeba17c1f10\",\n    \"m_Id\": 1,\n    \"m_DisplayName\":
    \"Edge2\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Edge2\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    1.0,\n        \"y\": 1.0,\n        \"z\": 1.0,\n        \"w\": 1.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"c0a7c63af759452e9b17f1ea30380acd\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"G\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"G\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SmoothstepNode\",\n   
    \"m_ObjectId\": \"c59ea2727b394e3491eb5d6f13bef194\",\n    \"m_Group\": {\n       
    \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n    },\n    \"m_Name\": \"Smoothstep\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -981.9998779296875,\n           
    \"y\": 1563.9998779296875,\n            \"width\": 208.0,\n            \"height\":
    326.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"de97515d0cec45ec9a3463352c119fc7\"\n        },\n        {\n            \"m_Id\":
    \"0a41789aa9d947df9806a26ef72405cb\"\n        },\n        {\n            \"m_Id\":
    \"31afe94076bc4dd6ba3c55b920a908e5\"\n        },\n        {\n            \"m_Id\":
    \"843094b108254daead36025349a58806\"\n        }\n    ],\n    \"synonyms\": [\n       
    \"curve\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n   
    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.SubtractNode\",\n    \"m_ObjectId\":
    \"c72e7019c51c4b8aac9400aafccaa084\",\n    \"m_Group\": {\n        \"m_Id\":
    \"ae815c96fb9547a493f35d348aa0d75a\"\n    },\n    \"m_Name\": \"Subtract\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1219.0001220703125,\n           
    \"y\": 312.0000305175781,\n            \"width\": 208.00006103515626,\n           
    \"height\": 302.0000305175781\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"6012d804a2f944d88931ea45be8ec851\"\n        },\n       
    {\n            \"m_Id\": \"0f8c97037b3a44a19beeb82d92ea1428\"\n        },\n       
    {\n            \"m_Id\": \"05427a293164462ca391a1ce809750e1\"\n        }\n   
    ],\n    \"synonyms\": [\n        \"subtraction\",\n        \"remove\",\n       
    \"minus\",\n        \"take away\"\n    ],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n    \"m_ObjectId\":
    \"c836079eecc44167bdbacb66aa7786a2\",\n    \"m_Group\": {\n        \"m_Id\":
    \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Emission\",\n    \"m_DrawState\":
    {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n            \"serializedVersion\":
    \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n            \"width\":
    0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\": [\n       
    {\n            \"m_Id\": \"f5db0d7a5b634f22ac27e1810cb809aa\"\n        }\n   
    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Emission\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.TangentMaterialSlot\",\n    \"m_ObjectId\": \"c98eca849ce844fea8e3a1d7448d044e\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Tangent\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Tangent\",\n    \"m_StageCapability\":
    1,\n    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0\n    },\n    \"m_Labels\": [],\n    \"m_Space\": 0\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"cd781165e39f4cd4bd5d19671f1622c5\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_MaskMax\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget\",\n   
    \"m_ObjectId\": \"ce4f2724893d447490729e3f5ddfa7d1\",\n    \"m_Datas\": [],\n   
    \"m_ActiveSubTarget\": {\n        \"m_Id\": \"2c40dcb158de49b096f908860c2a67c1\"\n   
    },\n    \"m_AllowMaterialOverride\": false,\n    \"m_SurfaceType\": 1,\n    \"m_ZTestMode\":
    4,\n    \"m_ZWriteControl\": 0,\n    \"m_AlphaMode\": 0,\n    \"m_RenderFace\":
    2,\n    \"m_AlphaClip\": false,\n    \"m_CastShadows\": true,\n    \"m_ReceiveShadows\":
    true,\n    \"m_DisableTint\": false,\n    \"m_AdditionalMotionVectorMode\": 0,\n   
    \"m_AlembicMotionVectors\": false,\n    \"m_SupportsLODCrossFade\": false,\n   
    \"m_CustomEditorGUI\": \"\",\n    \"m_SupportVFX\": false\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.CategoryData\",\n    \"m_ObjectId\":
    \"ceab22b886714561888df00cfd3aef15\",\n    \"m_Name\": \"\",\n    \"m_ChildObjectList\":
    [\n        {\n            \"m_Id\": \"292d1919291d410489337c647c1a64b2\"\n       
    },\n        {\n            \"m_Id\": \"d95897c126284153826db2fbfc1c7729\"\n       
    },\n        {\n            \"m_Id\": \"f65887f5163e49a8bddd9e7f7cd8053f\"\n       
    }\n    ]\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n   
    \"m_ObjectId\": \"d5095dea1be74c5e9775c8c9b765844c\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Alpha\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Alpha\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    1.0,\n    \"m_DefaultValue\": 1.0,\n    \"m_Labels\": []\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"d7c7c0fe6f724b41a179a9cf22c12e9b\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 1,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty\",\n   
    \"m_ObjectId\": \"d95897c126284153826db2fbfc1c7729\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"f5ef20f3-9cc2-43f9-8e2c-f293a38bda8c\"\n    },\n    \"m_Name\":
    \"_MaskMax\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"_MaskMax\",\n    \"m_DefaultReferenceName\": \"_MaskMax\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"de97515d0cec45ec9a3463352c119fc7\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Edge1\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Edge1\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 1,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.PositionNode\",\n    \"m_ObjectId\": \"e3b40896758447e5ad924a00aed0caec\",\n   
    \"m_Group\": {\n        \"m_Id\": \"\"\n    },\n    \"m_Name\": \"Position\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -2168.0,\n            \"y\":
    720.0,\n            \"width\": 207.9998779296875,\n            \"height\": 314.0001220703125\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"849defb8a28349cebad3bc4e7b474dc2\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"location\"\n    ],\n    \"m_Precision\":
    1,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    2,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    },\n   
    \"m_Space\": 2,\n    \"m_PositionSource\": 0\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"e4360ae57c884e1a8ab3eb6f1133555a\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"Edge1\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Edge1\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.Vector1MaterialSlot\",\n    \"m_ObjectId\": \"e5016cc40ebc4ed5b55713463d0cadb1\",\n   
    \"m_Id\": 0,\n    \"m_DisplayName\": \"Metallic\",\n    \"m_SlotType\": 0,\n   
    \"m_Hidden\": false,\n    \"m_ShaderOutputName\": \"Metallic\",\n    \"m_StageCapability\":
    2,\n    \"m_Value\": 0.0,\n    \"m_DefaultValue\": 0.0,\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"e7dc3bc1251a4e4fb358d327cc4269f7\",\n    \"m_Group\": {\n       
    \"m_Id\": \"f788b5c3888e46cf93e5fdd47f47ff37\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1612.9998779296875,\n           
    \"y\": 770.0,\n            \"width\": 129.0,\n            \"height\": 33.99993896484375\n       
    }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"f102dc10ef52417cbc18e3b756fe4301\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_Property\": {\n       
    \"m_Id\": \"292d1919291d410489337c647c1a64b2\"\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"edf4e042ae9c440ebe9cd843b867e180\",\n    \"m_Id\": 2,\n   
    \"m_DisplayName\": \"In\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"In\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.Vector3MaterialSlot\",\n    \"m_ObjectId\":
    \"f102dc10ef52417cbc18e3b756fe4301\",\n    \"m_Id\": 0,\n    \"m_DisplayName\":
    \"_MaskMin\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\":
    0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n    \"m_Labels\": []\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"f167fb43700741478a9164302433a0b7\",\n    \"m_Group\": {\n       
    \"m_Id\": \"ae815c96fb9547a493f35d348aa0d75a\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1386.0001220703125,\n           
    \"y\": 356.0000305175781,\n            \"width\": 152.0,\n            \"height\":
    34.0\n        }\n    },\n    \"m_Slots\": [\n        {\n            \"m_Id\":
    \"a098710806644fada95a65a003a9f1e8\"\n        }\n    ],\n    \"synonyms\": [],\n   
    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"f65887f5163e49a8bddd9e7f7cd8053f\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"f38bdccc227148f6b5954817abef7f47\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.Occlusion\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"8e75cd85e92c4f2face7d300bd1a0eec\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.Occlusion\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.SmoothstepNode\",\n    \"m_ObjectId\": \"f3e6469215b5470e8b15a0efc36f535e\",\n   
    \"m_Group\": {\n        \"m_Id\": \"f788b5c3888e46cf93e5fdd47f47ff37\"\n    },\n   
    \"m_Name\": \"Smoothstep\",\n    \"m_DrawState\": {\n        \"m_Expanded\":
    true,\n        \"m_Position\": {\n            \"serializedVersion\": \"2\",\n           
    \"x\": -1256.0001220703125,\n            \"y\": 722.0000610351563,\n           
    \"width\": 208.0,\n            \"height\": 326.00006103515627\n        }\n   
    },\n    \"m_Slots\": [\n        {\n            \"m_Id\": \"1cd2932b444042f3a53f8bacd3de5849\"\n       
    },\n        {\n            \"m_Id\": \"90b37e6da5ab446e9604dd4b7b5b7067\"\n       
    },\n        {\n            \"m_Id\": \"2cb511b74825426880695c6fd3cd2daf\"\n       
    },\n        {\n            \"m_Id\": \"ffb297f385ce42c99e42825288da95df\"\n       
    }\n    ],\n    \"synonyms\": [\n        \"curve\"\n    ],\n    \"m_Precision\":
    0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\":
    0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\": []\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.ColorRGBMaterialSlot\",\n   
    \"m_ObjectId\": \"f5db0d7a5b634f22ac27e1810cb809aa\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"Emission\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"Emission\",\n    \"m_StageCapability\": 2,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0\n    },\n   
    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\":
    0.0\n    },\n    \"m_Labels\": [],\n    \"m_ColorMode\": 1,\n    \"m_DefaultColor\":
    {\n        \"r\": 0.0,\n        \"g\": 0.0,\n        \"b\": 0.0,\n        \"a\":
    1.0\n    }\n}\n\n{\n    \"m_SGVersion\": 1,\n    \"m_Type\": \"UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty\",\n   
    \"m_ObjectId\": \"f65887f5163e49a8bddd9e7f7cd8053f\",\n    \"m_Guid\": {\n       
    \"m_GuidSerialized\": \"e8224adf-**************-aaa6de5f207f\"\n    },\n    \"m_Name\":
    \"_FadeDistance\",\n    \"m_DefaultRefNameVersion\": 1,\n    \"m_RefNameGeneratedByDisplayName\":
    \"_FadeDistance\",\n    \"m_DefaultReferenceName\": \"_FadeDistance\",\n    \"m_OverrideReferenceName\":
    \"\",\n    \"m_GeneratePropertyBlock\": true,\n    \"m_UseCustomSlotLabel\":
    false,\n    \"m_CustomSlotLabel\": \"\",\n    \"m_DismissedVersion\": 0,\n   
    \"m_Precision\": 0,\n    \"overrideHLSLDeclaration\": false,\n    \"hlslDeclarationOverride\":
    0,\n    \"m_Hidden\": false,\n    \"m_Value\": 0.0,\n    \"m_FloatType\": 0,\n   
    \"m_RangeValues\": {\n        \"x\": 0.0,\n        \"y\": 1.0\n    }\n}\n\n{\n   
    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.PropertyNode\",\n   
    \"m_ObjectId\": \"f7570d19ef694e49aa8155a6a7f5b128\",\n    \"m_Group\": {\n       
    \"m_Id\": \"ada33b48087b42959b5a8b40cb8e543d\"\n    },\n    \"m_Name\": \"Property\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": -1382.999755859375,\n           
    \"y\": 1583.9998779296875,\n            \"width\": 131.9998779296875,\n           
    \"height\": 34.0\n        }\n    },\n    \"m_Slots\": [\n        {\n           
    \"m_Id\": \"48ced890a367436a8d2d2813ac161dc7\"\n        }\n    ],\n    \"synonyms\":
    [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\": true,\n    \"m_DismissedVersion\":
    0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\": {\n        \"m_SerializableColors\":
    []\n    },\n    \"m_Property\": {\n        \"m_Id\": \"d95897c126284153826db2fbfc1c7729\"\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.GroupData\",\n   
    \"m_ObjectId\": \"f788b5c3888e46cf93e5fdd47f47ff37\",\n    \"m_Title\": \"Borda
    Inferior\",\n    \"m_Position\": {\n        \"x\": -1641.0,\n        \"y\": 662.9999389648438\n   
    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.BlockNode\",\n   
    \"m_ObjectId\": \"f9cc89d2657247ea865e2acf5b0260f2\",\n    \"m_Group\": {\n       
    \"m_Id\": \"\"\n    },\n    \"m_Name\": \"SurfaceDescription.BaseColor\",\n   
    \"m_DrawState\": {\n        \"m_Expanded\": true,\n        \"m_Position\": {\n           
    \"serializedVersion\": \"2\",\n            \"x\": 0.0,\n            \"y\": 0.0,\n           
    \"width\": 0.0,\n            \"height\": 0.0\n        }\n    },\n    \"m_Slots\":
    [\n        {\n            \"m_Id\": \"3bab6fb353ec4b6fae056449f457a118\"\n       
    }\n    ],\n    \"synonyms\": [],\n    \"m_Precision\": 0,\n    \"m_PreviewExpanded\":
    true,\n    \"m_DismissedVersion\": 0,\n    \"m_PreviewMode\": 0,\n    \"m_CustomColors\":
    {\n        \"m_SerializableColors\": []\n    },\n    \"m_SerializedDescriptor\":
    \"SurfaceDescription.BaseColor\"\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\":
    \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\": \"fcd3b85a2f7f4d17851b30bdf38d3d34\",\n   
    \"m_Id\": 2,\n    \"m_DisplayName\": \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\":
    false,\n    \"m_ShaderOutputName\": \"Out\",\n    \"m_StageCapability\": 3,\n   
    \"m_Value\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n       
    \"w\": 0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\":
    0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\":
    0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n   
    \"m_ObjectId\": \"fef73bb032e2437f96461dfb1d846cd9\",\n    \"m_Id\": 1,\n   
    \"m_DisplayName\": \"B\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"B\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    },\n    \"m_DefaultValue\": {\n        \"x\": 0.0,\n        \"y\": 0.0,\n       
    \"z\": 0.0,\n        \"w\": 0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n   
    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicVectorMaterialSlot\",\n    \"m_ObjectId\":
    \"ffb297f385ce42c99e42825288da95df\",\n    \"m_Id\": 3,\n    \"m_DisplayName\":
    \"Out\",\n    \"m_SlotType\": 1,\n    \"m_Hidden\": false,\n    \"m_ShaderOutputName\":
    \"Out\",\n    \"m_StageCapability\": 3,\n    \"m_Value\": {\n        \"x\": 0.0,\n       
    \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"x\": 0.0,\n        \"y\": 0.0,\n        \"z\": 0.0,\n        \"w\":
    0.0\n    }\n}\n\n{\n    \"m_SGVersion\": 0,\n    \"m_Type\": \"UnityEditor.ShaderGraph.DynamicValueMaterialSlot\",\n   
    \"m_ObjectId\": \"ffcea7418658489e896c403c7e6fdbb4\",\n    \"m_Id\": 0,\n   
    \"m_DisplayName\": \"A\",\n    \"m_SlotType\": 0,\n    \"m_Hidden\": false,\n   
    \"m_ShaderOutputName\": \"A\",\n    \"m_StageCapability\": 3,\n    \"m_Value\":
    {\n        \"e00\": 0.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 0.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 0.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 0.0\n    },\n    \"m_DefaultValue\":
    {\n        \"e00\": 1.0,\n        \"e01\": 0.0,\n        \"e02\": 0.0,\n       
    \"e03\": 0.0,\n        \"e10\": 0.0,\n        \"e11\": 1.0,\n        \"e12\":
    0.0,\n        \"e13\": 0.0,\n        \"e20\": 0.0,\n        \"e21\": 0.0,\n       
    \"e22\": 1.0,\n        \"e23\": 0.0,\n        \"e30\": 0.0,\n        \"e31\":
    0.0,\n        \"e32\": 0.0,\n        \"e33\": 1.0\n    }\n}\n\n"
  m_AssetMaybeChangedOnDisk: 0
  m_AssetMaybeDeleted: 1
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 401
    y: 542
    width: 920
    height: 452
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets
  m_LastFoldersGridSize: 16
  m_LastProjectPath: F:\Huggable X Horror
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 6af40000
    m_LastClickedID: 62570
    m_ExpandedIDs: 0000000018f400001af400001cf400001ef4000020f4000022f4000024f4000026f4000028f400002af400002cf400002ef4000030f4000032f4000034f4000036f4000038f400003af400003cf400003ef4000040f4000042f4000044f4000046f4000048f400004af400004cf400004ef4000050f4000052f4000054f4000056f4000058f400005af400005cf400005ef4000060f4000062f4000064f4000066f4000068f400006af40000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 10}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 0000000018f400001af400001cf400001ef4000020f4000022f4000024f4000026f4000028f400002af400002cf400002ef4000030f4000032f4000034f4000036f4000038f400003af400003cf400003ef4000040f4000042f4000044f4000046f4000048f400004af400004cf400004ef4000050f4000052f4000054f4000056f4000058f400005af400005cf400005ef4000060f4000062f4000064f4000066f4000068f400006af40000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 1
    m_ExpandedInstanceIDs: c2760000daaa01001eae01006c3102006a840000b6f10400c4370400e84c0500c6c100002a7f00003889000018800000e8990000327a00003c7a00007e880000c4870000b888000008c1ffffd4aaffff7c65ffff324fffff16c5ffff42960700e87a0000a62203002caa0000327b0000e47b000044ff0000e4a50000e0ae000020af0000267b00002cad0000be430300688b00000069010054790000bed8ffffda8dffff5277ffff9acdffff52c0ffff3e780000fc7800008c8affff2cec00002a8a0000b8430100b830010082870000a68800008c970100b448010064480100dc980000f28a0000e8200200beab0000a69100004090000080b8ffff6464feffa6760000a8760000ac760000e6770000f87b00007c7f0000ee78000058820000cc820000de83000072b901001a6f000050740100a0f60000d0f40000482a0300c40c010096d00000a4dc0000c4e50000c6060100686c010096f50000a2cc0000c4cc0000ead00000f097ffffda74ffff7cd8ffffbcce0000c0ce0000d8ce000068cd0000ea560100f2070100eae500000000000052d300009ad30000ccd3000094d30000aeafffff76ec0000ec7bffffd2e60000a8aaffff4c57ffffa63c010012eb0000700d010086ec000092e40000c4e40000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 10}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 171}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 200
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 402
    y: 542
    width: 919
    height: 452
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: -151
    m_ControlHash: 1412526313
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -10486
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1
    y: 487
    width: 920
    height: 452
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f817a38900380be47942905e17e7d39b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Timeline
    m_Image: {fileID: -9173288242387172643, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Timeline\u200B"
  m_Pos:
    serializedVersion: 2
    x: 310
    y: 563
    width: 960
    height: 431
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Preferences:
    editType: 0
    timeReferenceMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_SequenceHierarchy: {fileID: 0}
  m_SequencePath:
    m_SelectionRoot: 0
    m_SubElements: []
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12914, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animator
    m_Image: {fileID: -1673928668082335149, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Animator\u200B"
  m_Pos:
    serializedVersion: 2
    x: 551
    y: 512
    width: 767
    height: 482
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ViewTransforms:
    m_KeySerializationHelper:
    - {fileID: -4098499452981554584, guid: 3e88281b389a52147842584a1d6084e1, type: 2}
    - {fileID: 3306208633605317365, guid: 3e88281b389a52147842584a1d6084e1, type: 2}
    - {fileID: -8057130373195319508, guid: 3e88281b389a52147842584a1d6084e1, type: 2}
    - {fileID: 872848006190879649, guid: 3e88281b389a52147842584a1d6084e1, type: 2}
    - {fileID: 6692457829113811746, guid: 3e88281b389a52147842584a1d6084e1, type: 2}
    - {fileID: -1862113254902717571, guid: 046a37cc66bdeeb4fba6341cf4b758dc, type: 2}
    - {fileID: 872848006190879649, guid: ff4d3f9af7956d74d88f63c329147949, type: 2}
    m_ValueSerializationHelper:
    - e00: 0.80526316
      e01: 0
      e02: 0
      e03: -25.263153
      e10: 0
      e11: 0.80526316
      e12: 0
      e13: 116
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.80526316
      e01: 0
      e02: 0
      e03: -25.263153
      e10: 0
      e11: 0.80526316
      e12: 0
      e13: 136.13158
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.7553192
      e01: 0
      e02: 0
      e03: 0.957428
      e10: 0
      e11: 0.7553192
      e12: 0
      e13: 241.59575
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.3572996
      e01: 0
      e02: 0
      e03: 119.23172
      e10: 0
      e11: 0.3572996
      e12: 0
      e13: 153.0822
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.5657143
      e01: 0
      e02: 0
      e03: 147.17142
      e10: 0
      e11: 0.5657143
      e12: 0
      e13: 12.171417
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 0.80526316
      e01: 0
      e02: 0
      e03: -25.263153
      e10: 0
      e11: 0.80526316
      e12: 0
      e13: 136
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    - e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_PreviewAnimator: {fileID: 0}
  m_AnimatorController: {fileID: 9100000, guid: ff4d3f9af7956d74d88f63c329147949, type: 2}
  m_BreadCrumbs:
  - m_Target: {fileID: 872848006190879649, guid: ff4d3f9af7956d74d88f63c329147949, type: 2}
    m_ScrollPosition: {x: 0, y: 0}
  stateMachineGraph: {fileID: 0}
  stateMachineGraphGUI: {fileID: 0}
  blendTreeGraph: {fileID: 0}
  blendTreeGraphGUI: {fileID: 0}
  m_AutoLiveLink: 1
  m_MiniTool: 0
  m_LockTracker:
    m_IsLocked: 0
  m_CurrentEditor: 1
  m_LayerEditor:
    m_SelectedLayerIndex: 0
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12071, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animation
    m_Image: {fileID: -8166618308981325432, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Animation\u200B"
  m_Pos:
    serializedVersion: 2
    x: 402
    y: 511
    width: 916
    height: 483
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_LockTracker:
    m_IsLocked: 0
  m_LastSelectedObjectID: 58974
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1324
    y: 24
    width: 596
    height: 915
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: -303
    m_ControlHash: 1412526313
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 324
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12090, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 300, y: 250}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Occlusion
    m_Image: {fileID: -3571815596324412722, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Occlusion\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1297
    y: 87
    width: 630
    height: 915
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &26
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1323
    y: 79
    width: 596
    height: 915
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets/Shaders/Text Shaders
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/Shaders/Text Shaders
  m_LastFoldersGridSize: -1
  m_LastProjectPath: F:\Huggable X Horror
  m_LockTracker:
    m_IsLocked: 1
  m_FolderTreeState:
    scrollPos: {x: 0, y: 173}
    m_SelectedIDs: f4f40000
    m_LastClickedID: 62708
    m_ExpandedIDs: 0000000018f400001af400001cf400001ef4000020f4000022f4000024f4000026f4000028f400002af400002cf400002ef4000030f4000032f4000034f4000036f4000038f400003af400003cf400003ef4000040f4000042f4000044f4000046f4000048f400004af400004cf400004ef4000050f4000052f4000054f4000056f4000058f400005af400005cf400005ef4000060f4000062f4000064f4000066f4000068f400006af40000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 0000000018f400001af400001cf400001ef4000020f4000022f4000024f4000026f4000028f400002af400002cf400002ef4000030f4000032f4000034f4000036f4000038f400003af400003cf400003ef4000040f4000042f4000044f4000046f4000048f400004af400004cf400004ef4000050f4000052f4000054f4000056f4000058f400005af400005cf400005ef4000060f4000062f4000064f4000066f4000068f400006af40000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 0485feff
    m_LastClickedInstanceID: -97020
    m_HadKeyboardFocusLastEvent: 1
    m_ExpandedInstanceIDs: a2cc0000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 11}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 200
