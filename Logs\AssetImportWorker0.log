Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.42f1 (feb9a7235030) revision 16693671'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 32558 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\6000.0.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/Huggable X Horror
-logFile
Logs/AssetImportWorker0.log
-srvPort
52023
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: F:/Huggable X Horror
F:/Huggable X Horror
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8956]  Target information:

Player connection [8956]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3762907294 [EditorId] 3762907294 [Version] 1048832 [Id] WindowsEditor(7,Marlon-PC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [8956] Host joined multi-casting on [***********:54997]...
Player connection [8956] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 7.10 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 4.46 ms.
Initialize engine version: 6000.0.42f1 (feb9a7235030)
[Subsystems] Discovering subsystems at path F:/6000.0.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Huggable X Horror/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 (ID=0x2504)
    Vendor:   NVIDIA
    VRAM:     12115 MB
    Driver:   32.0.15.7270
Initialize mono
Mono path[0] = 'F:/6000.0.42f1/Editor/Data/Managed'
Mono path[1] = 'F:/6000.0.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/6000.0.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56284
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/6000.0.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002020 seconds.
- Loaded All Assemblies, in  0.413 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.364 seconds
Domain Reload Profiling: 777ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (169ms)
		LoadAssemblies (148ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (165ms)
				TypeCache.ScanAssembly (148ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (364ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.137 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 4.12 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Huggable X Horror
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.309 seconds
Domain Reload Profiling: 2444ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (861ms)
		LoadAssemblies (553ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (409ms)
			TypeCache.Refresh (306ms)
				TypeCache.ScanAssembly (284ms)
			BuildScriptInfoCaches (81ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1310ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1123ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (561ms)
			ProcessInitializeOnLoadMethodAttributes (379ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 5.96 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 309 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10026 unused Assets / (8.5 MB). Loaded Objects now: 10730.
Memory consumption went from 232.7 MB to 224.2 MB.
Total: 29.011600 ms (FindLiveObjects: 1.953300 ms CreateObjectMapping: 2.883300 ms MarkObjects: 13.620600 ms  DeleteObjects: 10.552500 ms)

========================================================================
Received Import Request.
  Time since last request: 56524.851114 seconds.
  path: Assets/GameManager/Text System/Base Visual Text Document.asset
  artifactKey: Guid(11bb7d533378b7a49a9da20ed721bd28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameManager/Text System/Base Visual Text Document.asset using Guid(11bb7d533378b7a49a9da20ed721bd28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '47d155997728e7c1f47d5cf139b883d3') in 0.0174327 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 50.194479 seconds.
  path: Assets/GameManager/Text System/Base Visual Text Document 1.asset
  artifactKey: Guid(abc865b5b27a1434398dff1f36abea86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameManager/Text System/Base Visual Text Document 1.asset using Guid(abc865b5b27a1434398dff1f36abea86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Serialization depth limit 10 exceeded at 'UnityEngine.Events::ArgumentCache.m_ObjectArgument'. There may be an object composition cycle in one or more of your serialized classes. Consider rearranging data or use `[SerializeReference]`. 

Serialization hierarchy:
11: UnityEngine.Events::ArgumentCache.m_ObjectArgument
10: UnityEngine.Events::PersistentCall.m_Arguments
9: UnityEngine.Events::PersistentCallGroup.m_Calls
8: UnityEngine.Events::UnityEventBase.m_PersistentCalls
7: SentenceDisplayData.OnSentenceStart
6: SentenceDisplayData.ReplacementOptions
5: SentenceDisplayData.ReplacementOptions
4: SentenceDisplayData.ReplacementOptions
3: SentenceDisplayData.ReplacementOptions
2: SentenceDisplayData.ReplacementOptions
1: PhraseData.Sentences
0: VisualTextDocument.Phrases
Serialization depth limit 10 exceeded at 'UnityEngine.Events::ArgumentCache.m_ObjectArgument'. There may be an object composition cycle in one or more of your serialized classes. Consider rearranging data or use `[SerializeReference]`. 

Serialization hierarchy:
11: UnityEngine.Events::ArgumentCache.m_ObjectArgument
10: UnityEngine.Events::PersistentCall.m_Arguments
9: UnityEngine.Events::PersistentCallGroup.m_Calls
8: UnityEngine.Events::UnityEventBase.m_PersistentCalls
7: SentenceDisplayData.OnSentenceStart
6: SentenceDisplayData.ReplacementOptions
5: SentenceDisplayData.ReplacementOptions
4: SentenceDisplayData.ReplacementOptions
3: SentenceDisplayData.ReplacementOptions
2: SentenceDisplayData.ReplacementOptions
1: SentenceDisplayData.ReplacementOptions
0: PhraseData.Sentences
 -> (artifact id: 'b303630e5f7b1ba03d6057b38f98ca00') in 0.0228649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 6.815933 seconds.
  path: Assets/GameManager/Text System/Base Visual Text Document.asset
  artifactKey: Guid(abc865b5b27a1434398dff1f36abea86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameManager/Text System/Base Visual Text Document.asset using Guid(abc865b5b27a1434398dff1f36abea86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46c4721654b11186d85d25a0e85a4814') in 0.0010763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.346 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 5.77 ms, found 10 plugins.
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.357 seconds
Domain Reload Profiling: 2706ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (884ms)
		LoadAssemblies (729ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (291ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (259ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1358ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1083ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (460ms)
			ProcessInitializeOnLoadMethodAttributes (430ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 7.34 ms, found 10 plugins.
Preloading 2 native plugins for Editor in 0.09 ms.
Unloading 39 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10024 unused Assets / (7.0 MB). Loaded Objects now: 10751.
Memory consumption went from 214.7 MB to 207.7 MB.
Total: 40.297000 ms (FindLiveObjects: 2.300100 ms CreateObjectMapping: 5.337500 ms MarkObjects: 20.886100 ms  DeleteObjects: 11.771600 ms)

Prepare: number of updated asset objects reloaded= 0
